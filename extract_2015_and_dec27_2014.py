import csv
from datetime import datetime

def extract_specific_dates(input_file, output_file):
    """
    Extract data from 2015 and December 27, 2014 from the CSV file
    """
    
    extracted_rows = []
    total_rows = 0
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        fieldnames = reader.fieldnames
        
        for row in reader:
            total_rows += 1
            chart_date = row['chart_date']
            
            try:
                # Parse the date
                date_obj = datetime.strptime(chart_date, '%Y-%m-%d')
                
                # Check if it's from 2015 or December 27, 2014
                if (date_obj.year == 2015) or (date_obj.year == 2014 and date_obj.month == 12 and date_obj.day == 27):
                    extracted_rows.append(row)
                    
            except ValueError:
                print(f"Warning: Could not parse date: {chart_date}")
                continue
    
    # Write extracted data to new CSV file
    with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(extracted_rows)
    
    return len(extracted_rows), total_rows

if __name__ == "__main__":
    input_file = "data/charts_data.csv"
    output_file = "data/charts_data_2015_and_dec27_2014.csv"
    
    print("Extracting data from 2015 and December 27, 2014...")
    extracted_count, total_count = extract_specific_dates(input_file, output_file)
    
    print(f"Extraction complete!")
    print(f"Total rows processed: {total_count}")
    print(f"Rows extracted: {extracted_count}")
    print(f"Output saved to: {output_file}")
    
    # Show some sample data
    print(f"\nFirst 10 rows of extracted data:")
    with open(output_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader):
            if i < 10:
                print(f"Date: {row['chart_date']}, Artist: {row['artist']}, Title: {row['title']}")
            else:
                break
    
    # Show date range summary
    print(f"\nAnalyzing date range in extracted data...")
    dates_found = set()
    with open(output_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            dates_found.add(row['chart_date'])
    
    sorted_dates = sorted(dates_found)
    print(f"Date range: {sorted_dates[0]} to {sorted_dates[-1]}")
    print(f"Total unique dates: {len(sorted_dates)}")
