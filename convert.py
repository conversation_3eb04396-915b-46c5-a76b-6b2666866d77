#!/usr/bin/env python3
"""
<PERSON>ript to convert artist names from list format to set format in CSV files.
Converts: ['Artist Name'] -> {"Artist Name"}
"""

import pandas as pd
import ast

def convert_artist_format(artist_str):
    """Convert artist string from list format to set format."""
    try:
        if isinstance(artist_str, str) and artist_str.startswith('[') and artist_str.endswith(']'):
            artist_list = ast.literal_eval(artist_str)
            
            if isinstance(artist_list, list):
                artists_formatted = ', '.join(f'"{artist}"' for artist in artist_list)
                return f"{{{artists_formatted}}}"
        
        return artist_str
            
    except (ValueError, SyntaxError, TypeError):
        return artist_str

def process_csv_file(input_file):
    """Process a CSV file to convert artist names format."""
    try:
        df = pd.read_csv(input_file)
        
        if 'artist' not in df.columns:
            print(f"Error: 'artist' column not found in {input_file}")
            return False
        
        df['artist'] = df['artist'].apply(convert_artist_format)
        df.to_csv(input_file, index=False)
        
        print(f"Successfully processed: {input_file}")
        return True
            
    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        return False

def main():
    """Main function to process CSV files."""
    files = ["data/charts_data.csv", "data/charts_data_2015_and_dec27_2014.csv"]
    
    for file_path in files:
        process_csv_file(file_path)

if __name__ == "__main__":
    main()